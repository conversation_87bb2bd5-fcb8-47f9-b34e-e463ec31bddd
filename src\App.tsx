
import { useState } from 'react';
import { ConfigProvider, Layout, Menu, Typography } from 'antd';
import { GoldOutlined, ExperimentOutlined } from '@ant-design/icons';
import zhCN from 'antd/locale/zh_CN';
import GoldPriceManagement from './pages/GoldPriceManagement';
import TestGoldPrice from './pages/TestGoldPrice';
import './App.css';

const { Header, Content } = Layout;
const { Title } = Typography;

function App() {
  const [selectedKey, setSelectedKey] = useState('management');

  const menuItems = [
    {
      key: 'management',
      icon: <GoldOutlined />,
      label: '黄金价格管理',
    },
    {
      key: 'test',
      icon: <ExperimentOutlined />,
      label: 'API测试',
    },
  ];

  const renderContent = () => {
    switch (selectedKey) {
      case 'management':
        return <GoldPriceManagement />;
      case 'test':
        return <TestGoldPrice />;
      default:
        return <GoldPriceManagement />;
    }
  };

  return (
    <ConfigProvider locale={zhCN}>
      <Layout style={{ minHeight: '100vh' }}>
        <Header style={{ display: 'flex', alignItems: 'center', background: '#fff', borderBottom: '1px solid #f0f0f0' }}>
          <Title level={3} style={{ margin: 0, marginRight: 'auto' }}>
            运营控制台
          </Title>
          <Menu
            mode="horizontal"
            selectedKeys={[selectedKey]}
            items={menuItems}
            style={{ border: 'none', background: 'transparent' }}
            onSelect={({ key }) => setSelectedKey(key)}
          />
        </Header>
        <Content style={{ background: '#f5f5f5' }}>
          {renderContent()}
        </Content>
      </Layout>
    </ConfigProvider>
  );
}

export default App;

import apiClient from "./api";


export interface ResultDTO<T = any> {
code: number;
message: string;
data: T;
}


// 后端实体对应
export interface PriceManual {
id?: number;
purityCode: string; // 成色/品类编码
itemCategory?: string; // 类别
pricePerG?: number; // 参考价(元/克)
status?: number; // 1=启用 0=停用
effectiveFrom?: string; // 生效时间（字符串或ISO）
remark?: string; // 备注
}


// 请求DTO（与后端 PriceManualDTO 对齐）
export interface PriceManualDTO {
id?: number;
purityCode?: string;
itemCategory?: string;
pricePerG?: number;
status?: number;
effectiveFrom?: string;
remark?: string;
}


export async function addGoldPrice(dto: PriceManualDTO): Promise<ResultDTO<string>> {
const resp = await apiClient.post<ResultDTO<string>>("/priceManual/addGoldPrice", dto);
return resp.data;
}


export async function updateGoldPrice(dto: PriceManualDTO): Promise<ResultDTO<number>> {
const resp = await apiClient.put<ResultDTO<number>>("/priceManual/updateGoldPrice", dto);
return resp.data;
}


export async function getGoldPrice(dto: PriceManualDTO): Promise<ResultDTO<PriceManual[]>> {
// 注意：你的后端等于查询条件是 eq(purity_code, dto.purityCode)，如果不传会生成 `= null` 导致空结果
const resp = await apiClient.post<ResultDTO<PriceManual[]>>("/priceManual/getGoldPrice", dto);
return resp.data;
}


export async function deleteGoldPrice(dto: Pick<PriceManualDTO, "id">): Promise<ResultDTO<number>> {
// axios.delete 的 body 需要放到 config.data
const resp = await apiClient.delete<ResultDTO<number>>("/priceManual/deleteGoldPrice", { data: dto });
return resp.data;
}
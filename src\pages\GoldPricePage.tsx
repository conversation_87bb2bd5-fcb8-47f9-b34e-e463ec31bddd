import React, { useState, useEffect } from "react";
import { Button, Table, Space, Card, Modal, Form, InputNumber, message, Popconfirm, DatePicker } from "antd";
import type { ColumnsType } from "antd/es/table";
import dayjs from "dayjs";
import { addGoldPrice,updateGoldPrice, getGoldPrice, deleteGoldPrice, type PriceManual, type PriceManualDTO } from "../api/goldPriceService";
import type { ResultDTO } from "../types/goldPrice";

const GoldPricePage: React.FC = () => {
    const [loading, setLoading] = useState(false);
    const [data, setData] = useState<PriceManual[]>([]);
    const [open, setOpen] = useState(false);
    const [editing, setEditing] = useState<PriceManual | null>(null);
    const [form] = Form.useForm<PriceManual>();
    const [searchPurity, setSearchPurity] = useState<String>("");

    const load = async (purityCode : String) => {
        if(!purityCode){
            setData([]);
            message.info("请输入成色/品类编码");
            return;
        }
        setLoading(true);
        try{
            const res : ResultDTO<PriceManual[]> = await getGoldPrice({purityCode});
            if(Array.isArray(res?.data)){
                setData(res.data);
                message.success(res?.message || "查询成功,共${res.data.length}条数据");
            }else{
                message.warning("查询失败,未返回数组数据");
            }
        }catch(e:any){
            message.error(e?.message || "查询失败");
        }finally{
            setLoading(false);
        }
    };
    useEffect(() => { /* no-op */ }, []);
    const onAdd = () => {
        setEditing(null);
        form.resetFields();
        // 默认启用
        form.setFieldsValue({ status: 1 });
        setOpen(true);
    };
    const onEdit = (record: PriceManual) => {
        setEditing(record);
        form.setFieldsValue({
        ...record,
        effectiveFrom: record.effectiveFrom ? (dayjs(record.effectiveFrom) as any) : undefined,
        } as any);
        setOpen(true);
    };
    const onDelete = async (record: PriceManual) => {
        try {
            const res = await deleteGoldPrice({ id: record.id });
            if (res?.code === 0) {
                message.success(res?.message || "删除成功");
                if (searchPurity) load(searchPurity);
            } else {
                message.error(res?.message || "删除失败");
            }
            } catch (e: any) {
            message.error(e?.message || "删除失败");
            }
        };

    const handleOk = async () => {
    try {
      const values = await form.validateFields();
      const payload: PriceManualDTO = {
        ...values,
        status: values.status ?? 0,
        effectiveFrom: (values as any).effectiveFrom ? (values as any).effectiveFrom.format("YYYY-MM-DD HH:mm:ss") : undefined,
      };

      if (editing?.id) {
        const res = await updateGoldPrice({ ...payload, id: editing.id });
        if (res?.code === 0) {
          message.success(res?.message || "更新成功");
          setOpen(false);
          if (searchPurity) load(searchPurity);
        } else {
          message.error(res?.message || "更新失败");
        }
      } else {
        const res = await addGoldPrice(payload);
        if (res?.code === 0) {
          message.success(res?.message || "添加成功");
          setOpen(false);
          if (searchPurity) load(searchPurity);
        } else {
          message.error(res?.message || "添加失败");
        }
      }
    } catch (e: any) {
      if (e?.errorFields) return; // 表单校验错误
      message.error(e?.message || "提交失败");
    }
  };
const columns: ColumnsType<PriceManual> = [
    { title: "ID", dataIndex: "id", width: 80 },
    { title: "成色/品类编码", dataIndex: "purityCode", width: 160 },
    { title: "类别", dataIndex: "itemCategory", width: 140 },
    { title: "参考价(元/克)", dataIndex: "pricePerG", width: 140, render: v => v ?? "-" },
    { title: "生效时间", dataIndex: "effectiveFrom", width: 180, render: v => v ? dayjs(v).format("YYYY-MM-DD HH:mm:ss") : "-" },
    { title: "状态", dataIndex: "status", width: 100, render: (v:number|undefined) => v === 1 ? <Tag color="green">启用</Tag> : <Tag>停用</Tag> },
    { title: "备注", dataIndex: "remark", ellipsis: true },
    {
      title: "操作",
      key: "action",
      fixed: "right",
      width: 180,
      render: (_, record) => (
        <Space>
          <Button size="small" onClick={() => onEdit(record)}>编辑</Button>
          <Popconfirm title="确定删除这条记录吗？" onConfirm={() => onDelete(record)}>
            <Button size="small" danger>删除</Button>
          </Popconfirm>
        </Space>
      )
    }
  ];

  return (
    <div className="p-4">
      <Card title="金价管理" extra={
        <Space>
          <Input
            placeholder="输入成色/品类编码，如 Au9999"
            value={searchPurity}
            onChange={e => setSearchPurity(e.target.value.trim())}
            onPressEnter={() => load(searchPurity)}
            style={{ width: 260 }}
          />
          <Button onClick={() => load(searchPurity)}>查询</Button>
          <Button type="primary" onClick={onAdd}>新增</Button>
        </Space>
      }>
        <Table
          rowKey={row => String(row.id ?? Math.random())}
          loading={loading}
          dataSource={data}
          columns={columns}
          pagination={{ pageSize: 10 }}
          scroll={{ x: 1100 }}
        />
      </Card>

      <Modal
        title={editing ? "编辑金价" : "新增金价"}
        open={open}
        onOk={handleOk}
        onCancel={() => setOpen(false)}
        destroyOnClose
      >
        <Form form={form} layout="vertical" preserve={false}>
          <Form.Item name="purityCode" label="成色/品类编码" rules={[{ required: true, message: "请输入成色/品类编码" }]}>
            <Input placeholder="如 Au9999 / K金14K" />
          </Form.Item>
          <Form.Item name="itemCategory" label="类别">
            <Input placeholder="如 金条 / 首饰 / 碎金" />
          </Form.Item>
          <Form.Item name="pricePerG" label="参考价(元/克)" rules={[{ required: true, message: "请输入参考价" }]}>
            <InputNumber style={{ width: "100%" }} min={0} step={0.01} placeholder="例如 560.88" />
          </Form.Item>
          <Form.Item name="effectiveFrom" label="生效时间">
            <DatePicker style={{ width: "100%" }} showTime format="YYYY-MM-DD HH:mm:ss" />
          </Form.Item>
          <Form.Item name="status" label="状态">
            <Switch checkedChildren="启用" unCheckedChildren="停用" checked={form.getFieldValue("status") === 1} onChange={(checked)=>form.setFieldValue("status", checked ? 1 : 0)} />
          </Form.Item>
          <Form.Item name="remark" label="备注">
            <Input.TextArea rows={3} placeholder="可选" maxLength={200} showCount />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default GoldPricePage;
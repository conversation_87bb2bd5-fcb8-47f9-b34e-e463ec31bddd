# 黄金价格管理系统前端

## 项目结构

```
src/
├── api/
│   ├── api.ts                    # 通用API客户端
│   └── goldPriceService.ts       # 黄金价格相关API服务
├── pages/
│   ├── GoldPriceManagement.tsx   # 黄金价格管理页面
│   └── TestGoldPrice.tsx         # API测试页面
├── types/
│   └── goldPrice.ts              # 黄金价格相关类型定义
└── App.tsx                       # 主应用组件
```

## 功能特性

### 黄金价格管理页面
- ✅ 查询黄金价格列表
- ✅ 新增黄金价格记录
- ✅ 编辑黄金价格记录
- ✅ 删除黄金价格记录
- ✅ 按纯度代码搜索
- ✅ 分页显示
- ✅ 响应式设计

### API测试页面
- ✅ 测试添加API
- ✅ 测试查询API
- ✅ 错误处理和日志输出

## 技术栈

- **React 19** - 前端框架
- **TypeScript** - 类型安全
- **Ant Design 5** - UI组件库
- **Axios** - HTTP客户端
- **Vite** - 构建工具

## API接口对应

| 前端方法 | 后端接口 | HTTP方法 | 说明 |
|---------|---------|----------|------|
| `addGoldPrice` | `/addGoldPrice` | POST | 添加黄金价格 |
| `updateGoldPrice` | `/updateGoldPrice` | PUT | 更新黄金价格 |
| `getGoldPrice` | `/getGoldPrice` | POST | 查询黄金价格 |
| `deleteGoldPrice` | `/deleteGoldPrice` | DELETE | 删除黄金价格 |

## 数据模型

### PriceManual
```typescript
interface PriceManual {
  id?: number;
  purityCode: string;
  price?: number;
  createTime?: string;
  updateTime?: string;
}
```

### PriceManualDTO
```typescript
interface PriceManualDTO {
  id?: number;
  purityCode: string;
  price?: number;
}
```

## 使用说明

1. **启动开发服务器**
   ```bash
   npm run dev
   ```

2. **配置后端API地址**
   - 编辑 `src/api/api.ts` 文件
   - 修改 `BASE_URL` 常量为你的后端服务地址

3. **测试API连接**
   - 访问 "API测试" 页面
   - 点击测试按钮验证后端连接

4. **使用管理功能**
   - 访问 "黄金价格管理" 页面
   - 进行增删改查操作

## 注意事项

- 确保后端服务已启动并可访问
- 检查CORS配置，确保前端可以访问后端API
- 在生产环境中需要配置正确的API基础地址
- 建议在后端添加数据验证和错误处理
